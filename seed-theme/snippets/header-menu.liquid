{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
  if show_dropdown_images
    if show_dropdown_images_autofill
      assign image = 'featured_image'
    else
      assign image = 'image'
    endif
  endif
  if custom_menu_items_per_columns
    assign items_limit = menu_items_limit
  endif
  assign subitems_limit = submenu_items_limit
  if layout
    assign promo_collection_list_blocks = blocks | where: 'type', 'promo_collection_list'
    assign promo_banner_blocks = blocks | where: 'type', 'promo_banners'
  endif
  assign sale_highlight_item = sale_highlight_item | strip | downcase
-%}

{%- for link in linklists[menu].links -%}
  {%- liquid
    assign link_title_downcase = link.title | downcase
    assign promo_collection = false
    for promo_collection_block in promo_collection_list_blocks
      if promo_collection_block.settings.menu_item == link.title
        assign promo_collection = promo_collection_block
        break
      endif
    endfor
    assign promo_banner = false
    for promo_banner_block in promo_banner_blocks
      if promo_banner_block.settings.menu_item == link.title
        assign promo_banner = promo_banner_block
        break
      endif
    endfor
  -%}
  {%- if layout == 'dropdown' and promo_collection == false and promo_banner == false -%}
    {%- assign link_title_downcase = link.title | downcase -%}
    <li
      class="{% unless show_chevrons %}no-arrow{% endunless %} {% if link.links != blank %}sub-static{% endif %}{% if link.active %} active{% endif %}{% if menubar_caps %} text-uppercase{% endif %} {% if main_menu_items_clickable == false and link.links != blank %} inactive{% endif %}{% if link.url == '#' and link.links != blank %} inactive{% endif %}"
      {% if link.active %}
        aria-current="page"
      {% endif %}
    >
      <a accesskey="{{ forloop.index }}" href="{{ link.url }}">
        {%- if link.object[image] and show_dropdown_images -%}
          <span class="img category{% if show_dropdown_images_rounded %} rounded{% endif %}">
            <picture class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
              <img
                src="{{ link.object[image] | image_url: width: 60, height: 60 }}"
                srcset="{{ link.object[image] | image_url: width: 30, height: 30 }} 1x,{{ link.object[image] | image_url: width: 60, height: 60 }} 2x"
                alt="{{ link.object[image].alt | default: link.title | escape }}"
                width="30"
                height="30"
                loading="lazy"
              >
            </picture>
          </span>
        {%- endif -%}
        {%- if link_title_downcase == sale_highlight_item and sale_highlight -%}
          <span class="s1bx overlay-sale">{{ link.title }}</span>
        {%- else -%}
          {{ link.title }}
        {%- endif -%}
      </a>
      {%- if link.links != blank %}
        <a href="./" class="toggle">{{ 'header.navigation.view_all' | t }}</a>
        <ul class="ff-{{ menu_subs_font }}">
          {%- for sub in link.links -%}
            <li>
              <ul>
                <li>
                  <a href="{{ sub.url }}">
                    {%- if sub.object[image] and show_dropdown_images -%}
                      <span class="img{% if show_dropdown_images_rounded %} rounded{% endif %}">
                        <picture class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
                          <img
                            src="{{ sub.object[image] | image_url: width: 60, height: 60 }}"
                            srcset="{{ sub.object[image] | image_url: width: 60, height: 60 }} 30w"
                            sizes="
                              (min-width: 1000px) 30px
                              0
                            "
                            alt="{{ sub.object[image].alt | default: sub.title | escape }}"
                            width="30"
                            height="30"
                            loading="lazy"
                          >
                        </picture>
                      </span>
                    {%- endif -%}
                    {{ sub.title }}
                  </a>
                  {%- if sub.links != blank %}
                    <a href="{{ sub.url }}" class="toggle">
                      {{- 'header.navigation.toggle_submenu' | t: item: sub.title -}}
                    </a>
                    <ul>
                      {%- for subsub in sub.links -%}
                        <li>
                          <a href="{{ subsub.url }}">
                            {%- if subsub.object[image] and show_dropdown_images and show_dropdown_images_subs -%}
                              <span class="img {% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}{% if show_dropdown_images_rounded %} rounded{% endif %}"
                                ><img
                                  src="{{ subsub.object[image] | image_url: width: 60, height: 60 }}"
                                  srcset="{{ subsub.object[image] | image_url: width: 60, height: 60 }} 30w"
                                  sizes="
                                    (min-width: 1000px) 30px
                                    0
                                  "
                                  alt="{{ subsub.object[image].alt | default: subsub.title | escape }}"
                                  width="30"
                                  height="30"
                                  loading="lazy"
                              ></span>
                            {%- endif -%}
                            {{ subsub.title }}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  {%- endif -%}
                </li>
              </ul>
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}
    </li>

  {%- else -%}
    <li
      class="{% unless show_chevrons %}no-arrow{% endunless %} {% if promo_collection or promo_banner %}no-sub promo {% endif %}{% if link.links != blank %}sub{% endif %}{% unless main_menu_show_lines %} no-inner-borders{% endunless %}{% if link.active %} active{% endif %}{% if menubar_caps %} text-uppercase{% endif %}{% if main_menu_items_clickable == false and link.links != blank %} inactive{% endif %}{% if link.url == '#' and link.links != blank %} inactive{% endif %}"
      {% if link.active %}
        aria-current="page"
      {% endif %}
      {% if promo_collection %}
        {{- promo_collection.shopify_attributes -}}
      {% elsif promo_banner %}
        {{- promo_banner.shopify_attributes -}}
      {% endif %}
      {% if section.settings.menu_compact %}
        style="--mr_menu: 0;"
      {% endif %}
    >
      <a accesskey="{{ forloop.index }}" href="{{ link.url }}">
        {%- if link.object[image] and show_dropdown_images -%}
          <span class="img category{% if show_dropdown_images_rounded %} rounded{% endif %}">
            <picture class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
              <img
                src="{{ link.object[image] | image_url: width: 60, height: 60 }}"
                srcset="{{ link.object[image] | image_url: width: 30, height: 30 }} 1x,{{ link.object[image] | image_url: width: 60, height: 60 }} 2x"
                alt="{{ link.object[image].alt | default: link.title | escape }}"
                width="30"
                height="30"
                loading="lazy"
              >
            </picture>
          </span>
        {%- endif -%}
        {%- if link_title_downcase == sale_highlight_item and sale_highlight -%}
          <span class="s1bx overlay-sale">{{ link.title }}</span>
        {%- else -%}
          {{ link.title }}
        {%- endif -%}
      </a>

      {% comment %} START PROMO COLLECTION LIST {% endcomment %}
      {%- if promo_collection -%}
        <a href="./" class="toggle">{{ 'header.navigation.view_all' | t }}</a>
        <ul class="palette-{{ settings.dropdown_color }} wide">
          <li>
            {%- liquid
              assign image_ratio = promo_collection.settings.image_ratio
              case image_ratio
                when 'portrait'
                  assign image_width = '240'
                  assign image_height = '300'
                when 'square'
                  assign image_width = '240'
                  assign image_height = '240'
                else
                  assign image_width = '300'
                  assign image_height = '240'
              endcase
              assign limit = promo_collection.settings.number_of_items
              case limit
                when 0
                  assign width_class = 'w20'
                when 2
                  assign width_class = 'w50'
                when 3
                  assign width_class = 'w33'
                when 4
                  assign width_class = 'w25'
                when 5
                  assign width_class = 'w20'
                when 6
                  assign width_class = 'w16'
                when 7
                  assign width_class = 'w14'
                else
                  assign width_class = 'w12'
              endcase
              assign img_width_limit = limit
              assign img_width = 100 | divided_by: img_width_limit

              if promo_collection.settings.show_link and promo_collection.settings.link_text != empty and promo_collection.settings.link_url != blank
                assign promo_collection_link = true
                assign button_color = promo_collection.settings.button_style | split: ' ' | first
                assign button_style = promo_collection.settings.button_style | split: ' ' | last
                assign is_link = false
                if button_style == 'link'
                  assign is_link = true
                endif
              endif

              if promo_collection.settings.title != empty
                assign show_header = true
              elsif promo_collection_link and promo_collection.settings.text_alignment == 'start'
                assign show_header = true
              endif

              if promo_collection.settings.title != empty
                assign container_div = true
              endif

              capture title_classes
                echo 'w900'
                if promo_collection.settings.text_alignment == 'center'
                  echo ' text-center align-center'
                endif
              endcapture
              if promo_collection.settings.layout == 'slider'
                if promo_collection.settings.collections.count > promo_collection.settings.number_of_items or promo_collection.settings.collections == blank
                  assign slider = true
                endif
              endif
            -%}

            {%- if promo_collection_link %}
              {%- capture promo_collection_link -%}
                            <p class="class-x link{% unless is_link %}-btn{% endunless %}"><a href="{{ promo_collection.settings.link_url }}" class="overlay-{{ button_color }} {% if is_link %}strong inline{% elsif button_style == 'inv' %}inv{% endif %}"><span {% if is_link %}class="link-underline"{% endif %}>{{ promo_collection.settings.link_text }}</span></a></p>
                          {%- endcapture %}
            {%- endif -%}

            {%- if show_header -%}
              <header class="cols{% if promo_collection_link and promo_collection.settings.show_link == false %} align-middle{% endif %}{% if promo_collection.settings.title == empty %} text-end{% endif %}">
                {%- if container_div -%}<div class="{{ title_classes }}">{%- endif -%}
                {%- if promo_collection.settings.title != empty -%}
                  <div
                    class="
                      title-styling
                      {% if promo_collection.settings.title_underline_style != 'none' %}
                        title-underline-none
                        {% if promo_collection.settings.title_underline_style contains 'accent' %}
                          title-underline-accent
                        {% elsif promo_collection.settings.title_underline_style contains 'gradient' %}
                          title-underline-gradient
                        {% endif %}
                        {% if promo_collection.settings.title_underline_style contains 'secondary_font' %}
                          title-underline-secondary-font
                        {% endif %}
                      {% endif %}
                    "
                  >
                    {{ promo_collection.settings.title }}
                  </div>
                {%- endif -%}
                {%- if container_div -%}</div>{%- endif -%}
                {%- if promo_collection_link and promo_collection.settings.text_alignment == 'start' -%}
                  {{ promo_collection_link | replace: 'class-x', 'mobile-hide' }}
                {%- endif -%}
              </header>
            {%- endif -%}
            <ul class="l4cl category no-img orientation-{{ image_ratio }} {{ width_class }} {% if slider %}slider{% elsif promo_collection.settings.mobile_layout != 'grid' %}mobile-compact{% endif %}{% if promo_collection.settings.mobile_layout == 'grid' %} w50-mobile{% endif %}{% if promo_collection.settings.collection_title_position == 'center' %} text-center{% endif %}">
              {%- if promo_collection.settings.collections == blank %}
                {%- for i in (1..promo_collection.settings.number_of_items) %}
                  {%- liquid
                    capture current
                      cycle 1, 2, 3, 4, 5
                    endcapture
                  -%}
                  <li>
                    <figure
                      class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}{% if promo_collection.settings.show_in_circle and promo_collection.settings.custom_image_ratio == 100 %} rounded{% endif %}"
                      style="--ratio:{{ promo_collection.settings.custom_image_ratio | divided_by: 100.0 }}"
                    >
                      <picture>
                        {% if promo_collection.settings.show_collection_titles == false %}
                          <a href="{{ collection.url }}"></a>
                        {% endif %}
                        {{ 'collection-' | append: current | placeholder_svg_tag: 'placeholder-svg' }}
                      </picture>
                    </figure>
                    {% if promo_collection.settings.show_collection_titles -%}
                      <{{ promo_collection.settings.collection_title_size }}
                        ><a
                          href="{{ collection.url }}"
                          class="{% if promo_collection.settings.show_collection_titles_underline %}inline{% endif %} overlay-content"
                          ><span
                            style="--btn_fz: var(--main_{{ promo_collection.settings.collection_title_size}})"
                            {% if promo_collection.settings.show_collection_titles_underline %}
                              class="link-underline"
                            {% endif %}
                            >Collection title</span
                          ></a
                        ></{{ promo_collection.settings.collection_title_size }}
                      >
                    {%- endif %}
                  </li>
                {%- endfor -%}
              {%- else %}
                {%- for collection in promo_collection.settings.collections -%}
                  {%- liquid
                    capture current
                      cycle 1, 2, 3, 4, 5, 6
                    endcapture
                  -%}
                  <li>
                    <figure class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}{% if promo_collection.settings.show_in_circle and promo_collection.settings.custom_image_ratio == 100 %} rounded{% endif %}">
                      <picture>
                        {% if promo_collection.settings.show_collection_titles == false %}
                          <a href="{{ collection.url }}"></a>
                        {% endif %}
                        {% if collection.featured_image %}
                          <img
                            src="{{ collection.featured_image | image_url: width: image_width, height: image_height }}"
                            srcset="
                              {% if promo_collection.settings.fill_images %}
                                {% render 'image-srcset', image: collection.featured_image, format: promo_collection.settings.custom_image_ratio, crop: 'center' %}
                              {% else %}
                                {% render 'image-srcset', image: collection.featured_image %}
                              {% endif %}
                            "
                            sizes="
                               {% if header_width < 2000 %}
                                (min-width: 1300px) {% if img_width == 100 %}calc({{ header_width }}px * 0.2){% else %}calc({{ header_width }}px * 0.{{ img_width }}){% endif %},
                              {% endif %}
                              (min-width: 760px) {% if img_width == 100 %}calc(100vw * 0.2){%-else -%}calc(100vw * 0.{{ img_width }}){% endif %},
                              141px
                            "
                            width="{{ image_width }}"
                            height="{{ image_height }}"
                            alt="{{ collection.featured_image.alt | default: collection.title | escape }}"
                            {% if promo_collection.settings.fill_images %}
                              class="filled"
                            {% endif %}
                            loading="lazy"
                          >
                        {% else %}
                          {{ 'collection-' | append: current | placeholder_svg_tag: 'placeholder-svg' }}
                        {% endif %}
                      </picture>
                    </figure>
                    {% if promo_collection.settings.show_collection_titles -%}
                      <{{ promo_collection.settings.collection_title_size }}
                        ><a
                          href="{{ collection.url }}"
                          class="{% if promo_collection.settings.show_collection_titles_underline %}inline{% endif %} overlay-content"
                          ><span
                            style="--btn_fz: var(--main_{{ promo_collection.settings.collection_title_size}})"
                            {% if promo_collection.settings.show_collection_titles_underline %}
                              class="link-underline"
                            {% endif %}
                          >
                            {{- collection.title -}}
                          </span></a
                        ></{{ promo_collection.settings.collection_title_size }}
                      >
                    {%- endif %}
                  </li>
                {%- endfor -%}
              {% endif -%}
            </ul>
            {%- if promo_collection_link and promo_collection.settings.text_alignment == 'center' -%}
              {{ promo_collection_link | replace: 'class-x', 'm0 text-center' }}
            {%- elsif promo_collection_link and promo_collection.settings.text_alignment == 'start' -%}
              {{ promo_collection_link | replace: 'class-x', 'm0 mobile-only' }}
            {%- endif -%}
          </li>
        </ul>
      {%- endif -%}
      {% comment %} END PROMO COLLECTION LIST {% endcomment %}

      {%- if promo_collection == false and link.links != blank or promo_banner %}
        <a href="./" class="toggle">{{ 'header.navigation.view_all' | t }}</a>
        <ul
          {% if layout %}
            class="wide"
          {% endif %}
        >
          {%- liquid
            assign split = false
            assign item_nr = 0
            assign total_nr_of_items = 0
            for sub in link.links
              assign nr_of_items = sub.links.size | at_most: subitems_limit
              assign total_nr_of_items = total_nr_of_items | plus: nr_of_items | plus: 1
            endfor
            if custom_menu_items_per_columns
              assign nr_of_items_per_col = items_limit | at_most: 15
            else
              if total_nr_of_items > 30
                assign nr_of_items_per_col = total_nr_of_items | divided_by: 4
              else
                assign nr_of_items_per_col = 6
              endif

              if promo_banner
                if total_nr_of_items > 12
                  assign nr_of_items_per_col = total_nr_of_items | divided_by: 4
                else
                  assign nr_of_items_per_col = 6
                endif
              endif
            endif
          -%}
          {%- for sub in link.links -%}
            {% if split or forloop.first %}
              {% assign split = false %}
              <li class="ff-{{ menu_subs_font }}">
                <ul>
            {% endif %}
            <li>
              {%- liquid
                assign item_nr = item_nr | plus: 1
                assign item_modulo = item_nr | modulo: nr_of_items_per_col
                if item_modulo == 0
                  assign split = true
                endif
              -%}
              <a href="{{ sub.url }}">
                {%- if sub.object[image] and show_dropdown_images -%}
                  <span class="img category{% if show_dropdown_images_rounded %} rounded{% endif %}">
                    <picture class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
                      <img
                        src="{{ sub.object[image] | image_url: width: 60, height: 60 }}"
                        srcset="{{ sub.object[image] | image_url: width: 60, height: 60 }} 30w"
                        sizes="
                          (min-width: 1000px) 30px
                          0
                        "
                        alt="{{ sub.object[image].alt | default: sub.title | escape }}"
                        width="30"
                        height="30"
                        loading="lazy"
                      >
                    </picture>
                  </span>
                {%- endif -%}
                {{ sub.title }}
              </a>
              {%- if sub.links != blank %}
                <a href="{{ sub.url }}" class="toggle">{{ 'header.navigation.toggle_submenu' | t: item: sub.title }}</a>
                <ul>
                  {%- for subsub in sub.links limit: subitems_limit -%}
                    {%- liquid
                      assign item_nr = item_nr | plus: 1
                      assign item_modulo = item_nr | modulo: nr_of_items_per_col
                      if item_modulo == 0
                        assign split = true
                      endif
                    -%}
                    <li>
                      <a href="{{ subsub.url }}">
                        {%- if subsub.object[image] and show_dropdown_images and show_dropdown_images_subs -%}
                          <span class="img category{% if show_dropdown_images_rounded %} rounded{% endif %}">
                            <picture class="{% if settings.multiply_collection_images == 'multiply' %}img-multiply{% elsif settings.multiply_collection_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
                              <img
                                src="{{ subsub.object[image] | image_url: width: 60, height: 60 }}"
                                srcset="{{ subsub.object[image] | image_url: width: 60, height: 60 }} 30w"
                                sizes="
                                  (min-width: 1000px) 30px
                                  0
                                "
                                alt="{{ subsub.object[image].alt | default: subsub.title | escape }}"
                                width="30"
                                height="30"
                                loading="lazy"
                              >
                            </picture>
                          </span>
                        {%- endif -%}
                        {{ subsub.title }}
                      </a>
                    </li>
                  {%- endfor -%}
                  {%- if sub.links.size > subitems_limit -%}
                    <li class="overlay-theme">
                      <a href="{{ sub.url }}" aria-label="{{ 'header.navigation.view_more' | t }}">
                        {{- 'header.navigation.view_more' | t -}}
                      </a>
                    </li>
                  {%- endif -%}
                </ul>
              {%- endif -%}
            </li>
            {% if split or forloop.last %}
              </ul>
              </li>
            {% endif %}
          {%- endfor -%}

          {% comment %} START PROMO BANNER COLUMNS {% endcomment %}
          {%- if promo_banner and promo_collection == false -%}
            <li
              {% if link.links != blank %}
                class="w50"
              {% endif %}
            >
              <ul class="l4ft dont-move{% if promo_banner.settings.image_zoom %} zoom{% endif %}{% if promo_banner.settings.layout_mobile == 'compact' %} mobile-compact{% endif %}">
                {%- liquid
                  assign nr_of_items = 0
                  if promo_banner.settings.enable_banner_1
                    assign nr_of_items = nr_of_items | plus: 1
                  endif
                  if promo_banner.settings.enable_banner_2
                    assign nr_of_items = nr_of_items | plus: 1
                  endif
                  assign width_class = 'w100'
                  assign img_width = 100
                  if nr_of_items == 2 and promo_banner.settings.layout == 'columns'
                    assign width_class = 'w50'
                    assign img_width = 50
                  endif

                  if promo_banner.settings.height == 'adapt'
                    assign padding_bottom = 1 | divided_by: promo_banner.settings.image_1.aspect_ratio | times: 100 | round: 2
                  else
                    assign aspect_ratio = promo_banner.settings.height | split: '/'
                    assign temp = aspect_ratio[0] | append: '.0'
                    assign ratio = aspect_ratio[1] | divided_by: temp | round: 2
                    assign padding_bottom = ratio | times: 100 | round: 2
                  endif

                  if promo_banner.settings.height_mobile == 'adapt'
                    assign padding_bottom_mobile = 1 | divided_by: promo_banner.settings.image_1.aspect_ratio | times: 100 | round: 2
                  else
                    assign aspect_ratio = promo_banner.settings.height_mobile | split: '/'
                    assign temp = aspect_ratio[0] | append: '.0'
                    assign ratio = aspect_ratio[1] | divided_by: temp | round: 2
                    assign padding_bottom_mobile = ratio | times: 100 | round: 2
                  endif
                -%}
                {%- for i in (1..2) -%}
                  {%- liquid
                    capture current
                      cycle 1, 2
                    endcapture
                    assign enable_banner_handle = 'enable_banner_x' | replace: 'x', forloop.index
                    assign enable_banner = promo_banner.settings[enable_banner_handle]
                    if enable_banner
                      assign image_handle = 'image_x' | replace: 'x', forloop.index
                      assign promo_image = promo_banner.settings[image_handle]
                      assign video_handle = 'video_x' | replace: 'x', forloop.index
                      assign video = promo_banner.settings[video_handle]
                      assign fill_images_handle = 'fill_images_x' | replace: 'x', forloop.index
                      assign fill_images = promo_banner.settings[fill_images_handle]
                      assign overlay_opacity_handle = 'overlay_opacity_x' | replace: 'x', forloop.index
                      assign overlay_opacity = promo_banner.settings[overlay_opacity_handle]
                      assign color_palette_handle = 'color_palette_x' | replace: 'x', forloop.index
                      assign color_palette = promo_banner.settings[color_palette_handle]
                      assign text_position_handle = 'text_position_y' | replace: 'y', forloop.index
                      assign text_position = promo_banner.settings[text_position_handle]
                      assign title_handle = 'title_x' | replace: 'x', forloop.index
                      assign title_underline = 'title_x_underline_style' | replace: 'x', forloop.index
                      assign title = promo_banner.settings[title_handle]
                      assign show_underline = promo_banner.settings[title_underline]
                      assign text_handle = 'text_y' | replace: 'y', forloop.index
                      assign text = promo_banner.settings[text_handle]
                      assign show_link_handle = 'show_link_x' | replace: 'x', forloop.index
                      assign show_link = promo_banner.settings[show_link_handle]
                      assign link_text_handle = 'link_text_y' | replace: 'y', forloop.index
                      assign link_text = promo_banner.settings[link_text_handle]
                      assign link_url_handle = 'link_url_x' | replace: 'x', forloop.index
                      assign link_url = promo_banner.settings[link_url_handle]
                      assign show_overlay_link_handle = 'show_overlay_link_x' | replace: 'x', forloop.index
                      assign show_overlay_link = promo_banner.settings[show_overlay_link_handle]
                      assign overlay_url_handle = 'overlay_url_x' | replace: 'x', forloop.index
                      assign overlay_url = promo_banner.settings[overlay_url_handle]
                      assign button_style_handle = 'button_style_x' | replace: 'x', forloop.index
                      assign button_style = promo_banner.settings[button_style_handle]
                      assign image_mobile_handle = 'image_mobile_x' | replace: 'x', forloop.index
                      assign image_mobile = promo_banner.settings[image_mobile_handle]
                      assign button_color = button_style | split: ' ' | first
                      assign button_style = button_style | split: ' ' | last
                      assign is_link = false
                      if button_style == 'link'
                        assign is_link = true
                      endif
                    endif
                  -%}
                  {%- if enable_banner -%}
                    <li
                      class="
                        block-{{ promo_banner.id }}-{{ forloop.index }}
                                text-{{ text_position | split: ' ' | last }}
                        {{ width_class }}
                      "
                      style="
                        --align_text: {{ text_position | split: ' ' | first }};
                        --justify_text: {{ text_position | split: ' ' | last }};
                        --padding_bottom: {{ padding_bottom }};
                        --padding_bottom_mobile: {{ padding_bottom_mobile }};
                      "
                    >
                      <div
                        class="
                          palette-{{ color_palette }}
                          module-color-palette
                          main
                        "
                      >
                        <figure>
                          <span class="img-overlay" style="opacity:{{ overlay_opacity | divided_by: 100.0 }}"></span>
                          {%- if video -%}
                            {%- if fill_images -%}
                              {{ video | video_tag: autoplay: true, loop: true, muted: true, controls: false }}
                            {%- else -%}
                              {{
                                video
                                | video_tag:
                                  autoplay: true,
                                  loop: true,
                                  muted: true,
                                  controls: false,
                                  style: 'object-fit:contain'
                              }}
                            {%- endif -%}
                          {%- elsif promo_image -%}
                            <picture>
                              {% if image_mobile %}
                                <img
                                  src="{{ image_mobile | image_url: width: 620 }}"
                                  srcset="{% render 'image-srcset', image: image_mobile %}"
                                  sizes="
                                    (min-width: 768px) 0,
                                    330px
                                  "
                                  width="620"
                                  height="700"
                                  alt="{{ image_mobile.alt | default: title | escape }}"
                                  style="object-position: {{ promo_image.presentation.focal_point }};{% unless fill_images %}object-fit: contain{% endunless %}"
                                  class="mobile-only"
                                  loading="lazy"
                                >
                              {% endif %}
                              <img
                                src="{{ promo_image | image_url: width: 620 }}"
                                srcset="{% render 'image-srcset', image: promo_image %}"
                                sizes="
                                  {% if header_width < 2000 %}
                                      {% if link.links != blank %}
                                      (min-width: 1300px) {% if img_width == 100 %}calc({{ header_width }}px * 0.5){% else %}calc(({{ header_width }}px * 0.{{ img_width }}) * 0.5 ){%- endif -%},
                                      {% else %}
                                       (min-width: 1300px) {% if img_width == 100 %}{{ header_width }}px{% else %}calc({{ header_width }}px * 0.{{ img_width }}){%- endif -%},
                                      {% endif %}
                                  {% else %}
                                     {% if link.links != blank %}
                                       (min-width: 1000px) {% if img_width == 100 %}50vw{% else %}calc(50vw * 0.{{ img_width }}){%- endif -%},
                                     {% else %}
                                       min-width: 1000px) {% if img_width == 100 %}100vw{% else %}calc(100vw * 0.{{ img_width }}){%- endif -%},
                                     {% endif %}
                                   {% endif %}
                                   {% if image_mobile %}0{% else %}330px{% endif %}
                                "
                                width="620"
                                height="700"
                                alt="{{ promo_image.alt | default: title | escape }}"
                                style="object-position: {{ promo_image.presentation.focal_point }};{% unless fill_images %}object-fit: contain{% endunless %}"
                                {% if image_mobile %}
                                  class="mobile-hide"
                                {% endif %}
                                loading="lazy"
                              >
                            </picture>
                          {% else %}
                            <picture>
                              {{ 'lifestyle-' | append: current | placeholder_svg_tag: 'placeholder-svg' }}
                            </picture>
                          {% endif %}
                        </figure>
                        {%- if promo_banner.settings.show_content_below == false -%}
                          <div>
                            {%- if title != empty -%}
                              <div
                                class="
                                  {% if show_underline != 'none' %}
                                    title-underline-none
                                    {% if show_underline contains 'accent' %}
                                      title-underline-accent
                                    {% elsif show_underline contains 'gradient' %}
                                      title-underline-gradient
                                    {% endif %}
                                    {% if show_underline contains 'secondary_font' %}
                                      title-underline-secondary-font
                                    {% endif %}
                                  {% endif %}
                                "
                              >
                                {{ title }}
                              </div>
                            {%- endif -%}

                            {%- if text -%}{{ text }}{%- endif -%}
                            {%- if show_link and link_text != empty and link_url != blank -%}
                              <p class="link{% unless is_link %}-btn{% endunless %}">
                                <a
                                  href="{{ link_url }}"
                                  class="overlay-{{ button_color }} {% if is_link %}inline strong{% elsif button_style == 'inv' %}inv{% endif %}"
                                >
                                  {% if is_link %}<span>{% endif -%}
                                  {{- link_text -}}
                                  {%- if is_link -%}
                                    </span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>
                                  {%- endif %}
                                </a>
                              </p>
                            {%- endif %}
                            {%- unless video -%}
                              {%- if show_overlay_link and overlay_url != blank -%}
                                <a
                                  class="link-overlay"
                                  href="{{ overlay_url }}"
                                  aria-label="{{ title | escape | default: promo_image.alt | default: "Promo banner" }}"
                                ></a>
                              {%- endif -%}
                            {%- endunless -%}
                          </div>
                        {%- endif -%}
                      </div>
                      {%- if promo_banner.settings.show_content_below -%}
                        <div class="content">
                          {%- if title != empty -%}
                            <div
                              class="
                                {% if show_underline != 'none' %}
                                  title-underline-none
                                  {% if show_underline contains 'accent' %}
                                    title-underline-accent
                                  {% elsif show_underline contains 'gradient' %}
                                    title-underline-gradient
                                  {% endif %}
                                  {% if show_underline contains 'secondary_font' %}
                                    title-underline-secondary-font
                                  {% endif %}
                                {% endif %}
                              "
                            >
                              {{ title }}
                            </div>
                          {%- endif -%}
                          {%- if text -%}{{ text }}{%- endif -%}
                          {%- if show_link and link_text != empty and link_url != blank -%}
                            <p class="link{% unless is_link %}-btn{% endunless %}">
                              <a
                                href="{{ link_url }}"
                                class="overlay-{{ button_color }} {% if is_link %}inline strong{% elsif button_style == 'inv' %}inv{% endif %}"
                              >
                                {% if is_link %}<span>{% endif -%}
                                {{- link_text -}}
                                {%- if is_link -%}
                                  </span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>
                                {%- endif %}
                              </a>
                            </p>
                          {%- endif %}
                        </div>
                      {%- endif -%}
                    </li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </li>
          {%- endif -%}
          {% comment %} END PROMO BANNER COLUMNS {% endcomment %}
        </ul>
      {%- endif -%}
    </li>
  {%- endif -%}
{%- endfor -%}
{%- if layout -%}
  <li class="show-all sub-static">
    <a href="{{ routes.root_url }}" class="toggle" aria-label="{{ 'header.navigation.view_more' | t }}">
      {{- 'header.navigation.view_more' | t -}}
    </a>
  </li>
{%- endif -%}
<style>
  @media only screen and (max-width: 760px) {
      #shopify-section-{{ section.id }} .l4ft li[style*="--padding_bottom"] > .main {
          --padding_bottom: {{ padding_bottom_mobile }}!important;
      }
  }

  #shopify-section-{{ section.id }} .l4ft li {
      min-height: 0!important;
  }
  #shopify-section-{{ section.id }} .l4ft li[style*="--padding_bottom"] > .main {
      flex-direction: row !important;
      flex-wrap: nowrap !important;
      min-height: 0 !important;
      align-items: var(--align_text) !important;
      justify-content: var(--justify_text) !important;
  }

  #shopify-section-{{ section.id }} .l4ft li[style*="--padding_bottom"] > .main:after {
      content: "";
      display: block;
      padding-bottom: calc(var(--padding_bottom)* 1%);
  }
</style>
